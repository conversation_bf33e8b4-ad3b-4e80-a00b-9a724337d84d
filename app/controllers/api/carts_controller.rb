module Api
  class CartsController < BaseController
    include Authenticatable
    before_action :cart_service

    def show
      cart = @cart_service.find_or_create_cart
      render_success(cart_data(cart))
    end

    def add_item
      cart = @cart_service.add_item(params)
      render_success(cart_data(cart))
    rescue StandardError => e
      render_error(e.message)
    end

    def remove_item
      cart = @cart_service.remove_item(params[:variant_id])
      render_success(cart_data(cart))
    rescue StandardError => e
      render_error(e.message)
    end

    def update_item
      cart = @cart_service.update_item_quantity(params[:variant_id], params[:quantity])
      render_success(cart_data(cart))
    rescue StandardError => e
      render_error(e.message)
    end

    def checkout
      # For authenticated users, we don't need customer_access_token
      # We'll use the user's email and shopify_customer_id instead
      result = @cart_service.process_checkout
      # Include message in the data response
      response_data = result[:data].dup
      response_data[:message] = result[:message]
      render_success(response_data)
    rescue StandardError => e
      render_error(e.message)
    end

    private

    def cart_service
      @cart_service = CartService.new(@current_user, request.remote_ip)
    end

    def cart_data(cart)
      {
        id: cart.id.to_s,
        items: cart.items,
        total_price: cart.total_price,
        currency: cart.currency,
        items_count: cart.items_count,
      }
    end
  end
end
