module Api
  class BuyNowController < BaseController
    include Authenticatable

    def create_checkout
      buy_now_service = BuyNowService.new(@current_user, request.remote_ip)

      result = buy_now_service.create_checkout(
        params[:variant_id],
        params[:quantity] || 1
      )

      render_success(result)
    rescue StandardError => e
      render_error(e.message)
    end

    private

    def buy_now_params
      params.permit(:variant_id, :quantity)
    end
  end
end
