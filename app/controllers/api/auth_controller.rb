module Api
  class AuthController < BaseController
    def authenticate
      token = request.headers["Authorization"]&.gsub("Bearer ", "")
      result = auth_service.authenticate(token)
      render_success(result)
    rescue StandardError => e
      render_error(e.message)
    end

    private

    def auth_service
      @auth_service ||= AuthService.new
    end

    def render_success(result)
      render json: { token: result[:token], user: result[:user] }
    end

    def render_error(message)
      render json: { error: message }, status: :unprocessable_entity
    end
  end
end
