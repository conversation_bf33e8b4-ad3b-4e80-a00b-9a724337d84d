class CartService
  def initialize(user, buyer_ip = nil)
    @user = user
    @buyer_ip = buyer_ip
  end

  def find_or_create_cart
    @cart = @user.carts.where(status: "active").first
    @cart ||= @user.carts.create(status: "active")
  end

  def add_item(params)
    validate_cart_item_params(params)
    cart = find_or_create_cart

    cart.add_item(
      params[:variant_id],
      params[:product_id],
      params[:quantity].to_i,
      params[:price].to_f,
      params[:title]
    )

    raise cart.errors.full_messages.join(", ") unless cart.save
    cart
  end

  def remove_item(variant_id)
    raise "Variant ID is required" if variant_id.blank?

    cart = find_or_create_cart
    cart.remove_item(variant_id)

    raise cart.errors.full_messages.join(", ") unless cart.save
    cart
  end

  def update_item_quantity(variant_id, quantity)
    raise "Variant ID is required" if variant_id.blank?
    raise "Quantity must be greater than 0" if quantity.to_i <= 0

    cart = find_or_create_cart
    cart.update_item_quantity(variant_id, quantity.to_i)

    raise cart.errors.full_messages.join(", ") unless cart.save
    cart
  end

  def process_checkout
    cart = find_or_create_cart

    # Validate cart has items before attempting checkout
    raise "Cart is empty - add items before checkout" if cart.items.blank?

    shopify_service = ShopifyService.new(@buyer_ip)

    # For authenticated users, use their email and shopify_customer_id
    # instead of requiring a separate customer_access_token
    checkout = shopify_service.create_checkout(
      cart.items,
      @user.shopify_customer_id,
      nil, # customer_access_token not needed for authenticated users
      @user.email # pass user email for buyer identity
    )
    raise "Unable to process checkout request" unless checkout

    order_status, message, order_type = determine_order_status(checkout)
    order = create_order(checkout, order_status, cart)

    # Fail fast on cart update
    raise cart.errors.full_messages.join(", ") unless cart.update(status: "checked_out")

    {
      message: message,
      data: {
        checkout_url: checkout["webUrl"],
        order_id: order.id.to_s,
        shopify_order_id: checkout["id"],
        shopify_order_name: checkout["name"] || checkout["id"],
        total_price: checkout.dig("totalPrice", "amount") || cart.total_price,
        currency: checkout.dig("totalPrice", "currencyCode") || cart.currency,
        status: order_status,
        order_type: order_type,
        authenticated: true, # Always true for authenticated users
      },
    }
  end

  private

  def validate_cart_item_params(params)
    required_params = [:variant_id, :product_id, :quantity, :price]
    missing_params = required_params.select { |param| params[param].blank? }

    if missing_params.any?
      raise "Missing required parameters: #{missing_params.join(", ")}"
    end

    if params[:quantity].to_i <= 0
      raise "Quantity must be greater than 0"
    end

    if params[:price].to_f <= 0
      raise "Price must be greater than 0"
    end
  end

  def determine_order_status(checkout)
    if checkout["isDraft"].present?
      ["draft", "Draft order created - customer can complete payment via checkout URL", "draft_order"]
    elsif checkout["name"].present?
      ["completed", "Order created successfully in Shopify", "shopify_order"]
    else
      ["pending", "Checkout created - customer needs to complete payment", "checkout_cart"]
    end
  end

  def create_order(checkout, status, cart)
    order = Order.create(
      user: @user,
      shopify_order_id: checkout["id"],
      shopify_checkout_url: checkout["webUrl"],
      total_price: cart.total_price,
      currency: cart.currency,
      status: status,
      items: cart.items,
    )

    raise order.errors.full_messages.join(", ") unless order.persisted?
    order
  end
end
