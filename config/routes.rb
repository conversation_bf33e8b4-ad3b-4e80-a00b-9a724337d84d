Rails.application.routes.draw do
  namespace :api do
    post "auth/authenticate", to: "auth#authenticate"

    # Remove products routes - Flutter calls Shopify directly

    resource :cart, only: [:show] do
      post :add_item
      delete :remove_item
      patch :update_item
      post :checkout
    end

    # Buy Now endpoint
    post "buy_now", to: "buy_now#create_checkout"

    resources :orders, only: [:index, :show]
  end
end
