development:
  clients:
    default:
      database: travelgator_development
      hosts:
        - 0.0.0.0:27017
      options:
        # user: admin
        # password: password
        # auth_source: admin
        server_selection_timeout: 5

test:
  clients:
    default:
      database: travelgator_test
      hosts:
        - 0.0.0.0:27017
      options:
        server_selection_timeout: 5

production:
  clients:
    default:
      database: <%= ENV['MONGO_DATABASE'] %>
      hosts:
        - <%= ENV['MONGODB_HOST'] %>
      options:
        user: <%= ENV['MONGO_ROOT_USERNAME'] %>
        password: <%= ENV['MONGO_ROOT_PASSWORD'] %>
        auth_source: travelgator
        server_selection_timeout: 5
        max_pool_size: 30
        min_pool_size: 5
        wait_queue_timeout: 30
        connect_timeout: 30
        socket_timeout: 30
