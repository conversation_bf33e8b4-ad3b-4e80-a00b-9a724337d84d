source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "2.6.8"

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem "rails", "~> 5.2.0"
# Use postgresql as the database for Active Record
# gem 'pg', '>= 0.18', '< 2.0'
# Use Puma as the app server
gem "puma", "~> 3.11"
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
gem "jbuilder", "~> 2.5"
# Use Redis adapter to run Action Cable in production
# gem 'redis', '~> 4.0'
# Use ActiveModel has_secure_password
# gem 'bcrypt', '~> 3.1.7'

# Use ActiveStorage variant
# gem 'mini_magick', '~> 4.8'

# Use Capistrano for deployment
# gem 'capistrano-rails', group: :development

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", ">= 1.1.0", require: false
gem "dotenv-rails"
# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible
# gem 'rack-cors'

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem "byebug", platforms: [:mri, :mingw, :x64_mingw]
end

group :development do
  gem "listen", ">= 3.0.5", "< 3.2"
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem "spring"
  gem "spring-watcher-listen", "~> 2.0.0"
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: [:mingw, :mswin, :x64_mingw, :jruby]

gem "sass"
gem "haml"
gem "rack-cors", "~> 0.3.1"
gem "sidekiq"
gem "whenever"
gem "kaminari"
gem "mini_magick"
gem "colorize"
gem "rest-client"
gem "request_store"
gem "device_detector"
gem "mongoid", "6.4.0"
gem "kaminari-mongoid"
gem "friendly_id"
gem "redis"
gem "redis-namespace"
gem "roo-xls"
gem "axlsx", "2.1.0.pre"
gem "axlsx_rails"
gem "mongoid_enumerable"
gem "exception_notification"
gem "bumbler"
gem "recursive-open-struct"
gem "devise"
gem "ruby-kafka"

# gem 'acts_as_votable'
# gem 'acts_as_commentable'
# gem 'link_thumbnailer'
gem "money"
gem "money-rails", "~>1.12"
gem "faker"
gem "mongoid-autoinc"
# gem 'mongoid-slug'

# gem 'trackable', git: 'https://github.com/kooinam/trackable', path: '../trackable'
# gem 'trackable', git: 'https://github.com/kooinam/trackable', ref: 'a4e90f7fb0a29885e19081eb8b57ef132802023e'
gem "s3_uploader"
gem "fog-aws"
gem "aws-sdk-s3"

gem "stringex", git: "https://github.com/kooinam/stringex", ref: "98a0d491a8ca250d0c221db23a9c05e078f01a89"
gem "myinfo", git: "https://github.com/kooinam/myinfo-rails", ref: "2565377cc47060215bea1a6869a9229b61c8bb0c"
# gem 'myinfo', path: '../myinfo-rails'
gem "jwt"
gem "mongoid-slug"
gem "googleauth"
gem "google-apis-identitytoolkit_v3"
gem "stripe"
gem "lograge"
gem "prawn"
gem "prawn-table"

gem "telegram-bot"

gem "omniauth-facebook"
gem "koala"

group :development, :test do
  gem "rspec-rails", "~> 3.7"
  gem "faker"
  gem "better_errors"
  gem "binding_of_caller"
  gem "database_cleaner"
  gem "write_xlsx"
end

group :development, :test do
  gem "railroady"
  gem "rails-controller-testing"
end

# gem "gruf"

# gem "prometheus-client", git: "https://github.com/kooinam/client_ruby", ref: "1cfb1d6ff82506304a10db59612857874d6f3cd6"
